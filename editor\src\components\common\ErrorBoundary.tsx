/**
 * 错误边界组件
 * 用于捕获子组件中的JavaScript错误，并显示备用UI
 */
import { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null};
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新状态，下次渲染时显示备用UI
    return {
      hasError: true,
      error};
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 记录详细的错误信息
    console.error('❌ 错误边界捕获到错误:', error, errorInfo);

    // 如果是Modal相关的错误，尝试清理Modal状态
    if (error.message.includes('Modal') || error.message.includes('关闭') || error.message.includes('onCancel')) {
      console.warn('⚠️ 检测到Modal相关错误，尝试清理Modal状态');
      this.cleanupModalState();
    }

    // 如果是引擎相关的错误，尝试重置引擎状态
    if (error.message.includes('engine') || error.message.includes('Engine') || error.message.includes('引擎')) {
      console.warn('⚠️ 检测到引擎相关错误，尝试重置引擎状态');
      this.cleanupEngineState();
    }

    // 如果是网络相关的错误，提供重试机制
    if (error.message.includes('fetch') || error.message.includes('network') || error.message.includes('网络')) {
      console.warn('⚠️ 检测到网络相关错误');
    }
  }

  private cleanupModalState = () => {
    try {
      const modals = document.querySelectorAll('.ant-modal-mask');
      modals.forEach(modal => {
        if (modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
      });
      console.log('✅ Modal状态清理完成');
    } catch (cleanupError) {
      console.error('❌ 清理Modal状态失败:', cleanupError);
    }
  };

  private cleanupEngineState = () => {
    try {
      // 尝试清理引擎相关的全局状态
      if ((window as any).engineService) {
        (window as any).engineService.stop();
        console.log('✅ 引擎服务已停止');
      }
    } catch (cleanupError) {
      console.error('❌ 清理引擎状态失败:', cleanupError);
    }
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // 渲染备用UI
      return this.props.fallback;
    }

    return this.props.children;
  }
}
